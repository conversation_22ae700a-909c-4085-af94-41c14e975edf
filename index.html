<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>显示屏</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            font-family: 'Arial', sans-serif;
        }

        .monitor-container {
            position: relative;
        }

        .monitor {
            width: 1200px;
            height: 720px;
            background: #2c3e50;
            border-radius: 8px;
            padding: 8px 8px 25px 8px;
            box-shadow:
                0 20px 40px rgba(0, 0, 0, 0.3),
                inset 0 2px 4px rgba(255, 255, 255, 0.1);
            position: relative;
        }

        .screen {
            width: 100%;
            height: calc(100% - 17px);
            background: #000;
            border-radius: 4px;
            position: relative;
            overflow: hidden;
            border: 1px solid #34495e;
            box-shadow: inset 0 0 20px rgba(0, 0, 0, 0.8);
        }

        .screen::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(
                45deg,
                transparent 30%,
                rgba(255, 255, 255, 0.05) 50%,
                transparent 70%
            );
            pointer-events: none;
        }



        .monitor-base {
            width: 300px;
            height: 100px;
            background: linear-gradient(to bottom, #34495e, #2c3e50);
            border-radius: 0 0 20px 20px;
            margin: 0 auto;
            position: relative;
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }

        .monitor-stand {
            width: 120px;
            height: 60px;
            background: linear-gradient(to bottom, #2c3e50, #1a252f);
            border-radius: 10px;
            margin: 0 auto;
            position: relative;
            top: -10px;
        }

        .control-panel {
            position: absolute;
            bottom: 8px;
            right: 15px;
            display: flex;
            gap: 8px;
            align-items: center;
        }

        .power-button {
            width: 12px;
            height: 12px;
            background: #e74c3c;
            border-radius: 50%;
            box-shadow: 0 0 6px rgba(231, 76, 60, 0.5);
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .power-button:hover {
            box-shadow: 0 0 10px rgba(231, 76, 60, 0.8);
        }

        .power-button.on {
            background: #27ae60;
            box-shadow: 0 0 6px rgba(39, 174, 96, 0.5);
        }

        .power-button.on:hover {
            box-shadow: 0 0 10px rgba(39, 174, 96, 0.8);
        }

        .volume-controls {
            display: flex;
            gap: 4px;
        }

        .volume-button {
            width: 10px;
            height: 10px;
            background: #34495e;
            border-radius: 2px;
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 8px;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }

        .volume-button:hover {
            background: #2c3e50;
        }

        .mute-button {
            width: 12px;
            height: 10px;
            background: #f39c12;
            border-radius: 2px;
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 7px;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }

        .mute-button:hover {
            background: #e67e22;
        }

        .mute-button.muted {
            background: #e74c3c;
        }

        .mute-button.muted:hover {
            background: #c0392b;
        }

        .game-content {
            display: none;
        }

        .game-content.active {
            display: block;
        }

        .reflection {
            position: absolute;
            top: 10px;
            left: 10px;
            width: 100px;
            height: 60px;
            background: linear-gradient(
                135deg,
                rgba(255, 255, 255, 0.2) 0%,
                transparent 50%
            );
            border-radius: 10px;
            pointer-events: none;
        }

        .table {
            position: absolute;
            top: 80%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 900px;
            height: 120px;
        }

        .table-top {
            width: 100%;
            height: 20px;
            background: linear-gradient(to bottom, #8B4513, #654321);
            border-radius: 8px 8px 4px 4px;
            box-shadow:
                0 2px 4px rgba(0, 0, 0, 0.3),
                inset 0 1px 2px rgba(255, 255, 255, 0.1);
            position: relative;
        }

        .table-leg {
            position: absolute;
            width: 12px;
            height: 80px;
            background: linear-gradient(to right, #654321, #4A2C17);
            border-radius: 2px;
            top: 20px;
            box-shadow: 1px 0 2px rgba(0, 0, 0, 0.2);
        }

        .table-leg:nth-child(2) {
            left: 50px;
        }

        .table-leg:nth-child(3) {
            right: 50px;
        }

        .swatter {
            position: absolute;
            top: 35%;
            right: 30%;
            width: 100px;
            height: 140px;
            transform: rotate(-20deg);
            z-index: 2;
        }

        .swatter-handle {
            width: 12px;
            height: 70px;
            background: linear-gradient(to right, #333, #555);
            border-radius: 6px;
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            box-shadow: 1px 0 2px rgba(0, 0, 0, 0.4);
        }

        .swatter-head {
            width: 80px;
            height: 80px;
            border: 3px solid #333;
            border-radius: 8px;
            position: absolute;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            background:
                linear-gradient(90deg, transparent 48%, #333 48%, #333 52%, transparent 52%),
                linear-gradient(0deg, transparent 48%, #333 48%, #333 52%, transparent 52%),
                repeating-linear-gradient(90deg, transparent 0px, transparent 8px, #333 8px, #333 10px),
                repeating-linear-gradient(0deg, transparent 0px, transparent 8px, #333 8px, #333 10px);
            box-shadow: 0 3px 6px rgba(0, 0, 0, 0.3);
        }

        .swatter.swatting {
            animation: swat 0.25s ease-in-out;
        }

        @keyframes swat {
            0% {
                top: 35%;
                transform: rotate(-20deg);
            }
            50% {
                top: 65%;
                transform: rotate(-10deg);
            }
            100% {
                top: 35%;
                transform: rotate(-20deg);
            }
        }

        .character {
            position: absolute;
            top: 62%;
            left: 15%;
            width: 75px;
            height: 75px;
            background-image: url('ALazyPikaXD.gif');
            background-size: contain;
            background-position: center;
            background-repeat: no-repeat;
            z-index: 1;
            transition: all 0.1s ease-out;
        }

        .character.squashed {
            transform: scaleY(0.3) scaleX(1.2);
            transform-origin: bottom center;
        }

        .score-display {
            position: absolute;
            top: 20px;
            left: 20px;
            color: white;
            font-size: 24px;
            font-weight: bold;
            text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
            z-index: 3;
            font-family: 'Courier New', monospace;
        }








    </style>
</head>
<body>
    <div class="monitor-container">
        <div class="monitor">
            <div class="screen">
                <div class="reflection"></div>
                <div class="game-content">
                    <div class="score-display">Scores: <span id="score">0</span></div>
                    <div class="table">
                        <div class="table-top"></div>
                        <div class="table-leg"></div>
                        <div class="table-leg"></div>
                    </div>
                    <div class="swatter">
                        <div class="swatter-head"></div>
                        <div class="swatter-handle"></div>
                    </div>
                    <div class="character"></div>
                </div>
            </div>
            <div class="control-panel">
                <div class="volume-controls">
                    <div class="volume-button" id="volume-down">-</div>
                    <div class="volume-button" id="volume-up">+</div>
                    <div class="mute-button" id="mute-btn">♪</div>
                </div>
                <div class="power-button" id="power-btn"></div>
            </div>
        </div>
        <div class="monitor-base">
            <div class="monitor-stand"></div>
        </div>
    </div>

    <!-- 背景音乐 -->
    <audio id="bgm" loop>
        <source src="sounds/bgm.MP3" type="audio/mpeg">
        您的浏览器不支持音频播放。
    </audio>

    <!-- 音效 -->
    <audio id="shake-sound" preload="auto">
        <source src="sounds/shake.MP3" type="audio/mpeg">
    </audio>

    <audio id="hit-sound" preload="auto">
        <source src="sounds/hit.MP3" type="audio/mpeg">
    </audio>

    <audio id="hit2-sound" preload="auto">
        <source src="sounds/hit2.MP3" type="audio/mpeg">
    </audio>

    <script>
        const swatter = document.querySelector('.swatter');
        const screen = document.querySelector('.screen');
        const character = document.querySelector('.character');
        const scoreElement = document.getElementById('score');
        const gameContent = document.querySelector('.game-content');
        const powerBtn = document.getElementById('power-btn');
        const volumeUpBtn = document.getElementById('volume-up');
        const volumeDownBtn = document.getElementById('volume-down');
        const muteBtn = document.getElementById('mute-btn');
        const bgm = document.getElementById('bgm');
        const shakeSound = document.getElementById('shake-sound');
        const hitSound = document.getElementById('hit-sound');
        const hit2Sound = document.getElementById('hit2-sound');

        let isSwatting = false;
        let score = 0;
        let canScore = true; // 是否可以得分
        let isPoweredOn = false; // 是否开机
        let currentVolume = 0.5; // 当前音量
        let isMuted = false; // 是否静音
        let volumeBeforeMute = 0.5; // 静音前的音量

        // 智能角色对象
        const smartCharacter = {
            element: character,
            x: 15, // 当前X位置（百分比）
            y: 62, // 当前Y位置（百分比）
            targetX: 80, // 目标X位置
            speed: 0.08, // 正常移动速度（百分比/帧）- 很慢的悠闲移动
            panicSpeed: 1.2, // 恐慌时的速度 - 拍打时的快速逃跑
            currentSpeed: 0.08,
            direction: 1, // 1向右，-1向左
            isSquashed: false,
            isPanic: false,
            isMoving: true,
            animationFrame: null,

            // 初始化
            init() {
                this.updatePosition();
                this.startMoving();
            },

            // 更新DOM位置
            updatePosition() {
                this.element.style.left = this.x + '%';
                this.element.style.top = this.y + '%';
            },

            // 开始移动
            startMoving() {
                if (this.animationFrame) {
                    cancelAnimationFrame(this.animationFrame);
                }
                this.isMoving = true;
                this.move();
            },

            // 停止移动
            stopMoving() {
                this.isMoving = false;
                if (this.animationFrame) {
                    cancelAnimationFrame(this.animationFrame);
                }
            },

            // 移动逻辑
            move() {
                if (!this.isMoving || this.isSquashed) return;

                // 计算移动
                this.x += this.currentSpeed * this.direction;

                // 边界检测和转向
                if (this.x >= 80) {
                    this.x = 80;
                    this.direction = -1;
                } else if (this.x <= 15) {
                    this.x = 15;
                    this.direction = 1;
                }

                this.updatePosition();

                // 继续动画
                this.animationFrame = requestAnimationFrame(() => this.move());
            },

            // 进入恐慌状态
            enterPanic() {
                if (this.isSquashed) return;
                this.isPanic = true;
                this.currentSpeed = this.panicSpeed;

                // 恐慌时尝试逃离拍子
                const swatterRect = swatter.getBoundingClientRect();
                const characterRect = this.element.getBoundingClientRect();
                const swatterCenterX = swatterRect.left + swatterRect.width / 2;
                const characterCenterX = characterRect.left + characterRect.width / 2;

                // 根据拍子位置决定逃跑方向
                if (characterCenterX > swatterCenterX) {
                    this.direction = 1; // 向右逃
                } else {
                    this.direction = -1; // 向左逃
                }
            },

            // 退出恐慌状态
            exitPanic() {
                this.isPanic = false;
                this.currentSpeed = this.speed;
            },

            // 被压扁
            squash() {
                this.isSquashed = true;
                this.stopMoving();
                this.element.classList.add('squashed');

                // 2秒后恢复
                setTimeout(() => {
                    this.recover();
                }, 2000);
            },

            // 恢复正常
            recover() {
                this.isSquashed = false;
                this.isPanic = false;
                this.currentSpeed = this.speed;
                this.element.classList.remove('squashed');
                this.startMoving();
                canScore = true;
            },

            // 重置
            reset() {
                this.stopMoving();
                this.x = 15;
                this.y = 62;
                this.direction = 1;
                this.isSquashed = false;
                this.isPanic = false;
                this.currentSpeed = this.speed;
                this.element.classList.remove('squashed');
                this.updatePosition();
            }
        };

        // 初始化音频
        bgm.volume = currentVolume;

        // 设置音效音量
        function updateSoundEffectsVolume() {
            const effectVolume = isMuted ? 0 : currentVolume;
            shakeSound.volume = effectVolume;
            hitSound.volume = effectVolume;
            hit2Sound.volume = effectVolume;
        }

        // 播放音效
        function playSound(audioElement) {
            if (!isMuted) {
                audioElement.currentTime = 0; // 重置播放位置
                audioElement.play().catch(e => {
                    console.log('音效播放失败:', e);
                });
            }
        }

        // 初始化音效音量
        updateSoundEffectsVolume();

        // 开机/关机功能
        function togglePower() {
            isPoweredOn = !isPoweredOn;

            if (isPoweredOn) {
                // 开机
                gameContent.classList.add('active');
                powerBtn.classList.add('on');

                // 启动智能角色
                smartCharacter.init();

                // 播放背景音乐
                bgm.play().catch(e => {
                    console.log('音频播放失败:', e);
                });
            } else {
                // 关机/重启
                gameContent.classList.remove('active');
                powerBtn.classList.remove('on');

                // 停止背景音乐
                bgm.pause();
                bgm.currentTime = 0;

                // 重置游戏状态
                resetGame();
            }
        }

        // 重置游戏状态
        function resetGame() {
            score = 0;
            updateScore();
            isSwatting = false;
            canScore = true;

            // 重置智能角色
            smartCharacter.reset();

            // 清除拍子状态
            swatter.classList.remove('swatting');
        }

        // 音量控制
        function adjustVolume(delta) {
            if (isMuted) {
                // 如果当前是静音状态，先取消静音
                toggleMute();
            }
            currentVolume = Math.max(0, Math.min(1, currentVolume + delta));
            bgm.volume = currentVolume;
            updateSoundEffectsVolume();
        }

        // 静音/取消静音
        function toggleMute() {
            if (isMuted) {
                // 取消静音
                isMuted = false;
                currentVolume = volumeBeforeMute;
                bgm.volume = currentVolume;
                muteBtn.classList.remove('muted');
                muteBtn.textContent = '♪';
            } else {
                // 静音
                isMuted = true;
                volumeBeforeMute = currentVolume;
                currentVolume = 0;
                bgm.volume = 0;
                muteBtn.classList.add('muted');
                muteBtn.textContent = '✕';
            }
            updateSoundEffectsVolume();
        }

        // 更新分数显示
        function updateScore() {
            scoreElement.textContent = score;
        }

        // 增加分数
        function addScore(points) {
            if (canScore) {
                score += points;
                updateScore();
                canScore = false; // 防止重复得分
            }
        }

        // 检查拍子中心部分和角色是否重叠（更精确的碰撞检测）
        function checkCollision() {
            const swatterRect = swatter.getBoundingClientRect();
            const characterRect = character.getBoundingClientRect();

            // 只检测拍子头部的中心区域（大幅缩小碰撞区域）
            const swatterCenterX = swatterRect.left + swatterRect.width * 0.5;
            const swatterCenterY = swatterRect.top + swatterRect.height * 0.3; // 拍子头部位置
            const hitRadius = 25; // 有效击中半径，只有25像素

            const swatterHitBox = {
                left: swatterCenterX - hitRadius,
                right: swatterCenterX + hitRadius,
                top: swatterCenterY - hitRadius,
                bottom: swatterCenterY + hitRadius
            };

            // 角色的碰撞盒保持原样
            const characterBox = {
                left: characterRect.left + 5,
                right: characterRect.right - 5,
                top: characterRect.top + 5,
                bottom: characterRect.bottom - 5
            };

            return !(swatterHitBox.right < characterBox.left ||
                     swatterHitBox.left > characterBox.right ||
                     swatterHitBox.bottom < characterBox.top ||
                     swatterHitBox.top > characterBox.bottom);
        }



        // 鼠标按下时拍击
        screen.addEventListener('mousedown', function(e) {
            if (e.button === 0 && !isSwatting && isPoweredOn) { // 左键且不在拍击中且已开机
                isSwatting = true;

                // 角色进入恐慌状态
                smartCharacter.enterPanic();

                // 开始拍击动画
                swatter.classList.remove('swatting');
                void swatter.offsetWidth;
                swatter.classList.add('swatting');

                // 播放挥动音效
                playSound(shakeSound);

                // 在拍击到底部时检查碰撞
                setTimeout(() => {
                    if (checkCollision()) {
                        // 角色被压扁
                        smartCharacter.squash();

                        // 播放击中音效
                        playSound(hit2Sound);

                        // 增加分数（只有在可以得分时）
                        addScore(1);
                    } else {
                        // 没有击中，播放未击中音效
                        playSound(hitSound);
                    }
                }, 125);

                // 125ms后角色退出恐慌状态（拍子到底时）
                setTimeout(() => {
                    smartCharacter.exitPanic();
                }, 125);

                // 拍击动画完全结束，允许下次拍击
                setTimeout(() => {
                    swatter.classList.remove('swatting');
                    isSwatting = false;
                }, 250);
            }
        });



        // 按钮事件监听器
        powerBtn.addEventListener('click', togglePower);

        volumeUpBtn.addEventListener('click', function() {
            adjustVolume(0.1);
        });

        volumeDownBtn.addEventListener('click', function() {
            adjustVolume(-0.1);
        });

        muteBtn.addEventListener('click', toggleMute);

        // 键盘空格键触发
        document.addEventListener('keydown', function(e) {
            if (e.code === 'Space') {
                e.preventDefault();
                if (!isSwatting && isPoweredOn) {
                    // 模拟鼠标按下事件
                    const mouseEvent = new MouseEvent('mousedown', { button: 0 });
                    screen.dispatchEvent(mouseEvent);
                }
            }
        });
    </script>
</body>
</html>
